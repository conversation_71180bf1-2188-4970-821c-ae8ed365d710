import React from 'react'
import { Card, Typography, Alert, Space, Button, List } from 'antd'
import { 
  EnvironmentOutlined, 
  InfoCircleOutlined,
  WifiOutlined,
  MobileOutlined,
  HomeOutlined,
  SettingOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography

interface LocationTroubleshootProps {
  onClose: () => void
  errorCode?: number
}

const LocationTroubleshoot: React.FC<LocationTroubleshootProps> = ({ onClose, errorCode }) => {
  // Detect device type
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
  const isAndroid = /Android/.test(navigator.userAgent)

  const getTroubleshootSteps = () => {
    const commonSteps = [
      {
        icon: <MobileOutlined />,
        title: 'Kiểm tra GPS/Vị trí',
        description: '<PERSON><PERSON><PERSON> bảo dịch vụ vị trí được bật',
        actions: isMobile ? [
          isIOS ? 'Cài đặt > Quyền riêng tư & <PERSON><PERSON>o mật > Dịch vụ vị trí' : '<PERSON>à<PERSON> đặt > Vị trí',
          'Bật "Dịch vụ vị trí" hoặc "Vị trí"',
          isAndroid ? 'Chọn "Độ chính xác cao" trong Chế độ' : 'Đảm bảo "Định vị chính xác" được bật'
        ] : [
          'Kiểm tra cài đặt vị trí trong hệ điều hành',
          'Đảm bảo trình duyệt có quyền truy cập vị trí',
          'Thử khởi động lại trình duyệt'
        ]
      },
      {
        icon: <HomeOutlined />,
        title: 'Cải thiện tín hiệu',
        description: 'GPS hoạt động tốt hơn ở ngoài trời',
        actions: [
          'Di chuyển ra ngoài trời hoặc gần cửa sổ',
          'Tránh các tòa nhà cao tầng và hầm ngầm',
          'Đợi 10-30 giây để GPS định vị chính xác'
        ]
      },
      {
        icon: <WifiOutlined />,
        title: 'Kiểm tra kết nối mạng',
        description: 'Cần kết nối internet để xác định vị trí',
        actions: [
          'Kiểm tra kết nối WiFi hoặc 4G/5G',
          'Thử tắt/bật lại WiFi',
          'Kiểm tra tín hiệu mạng'
        ]
      },
      {
        icon: <SettingOutlined />,
        title: 'Kiểm tra quyền trình duyệt',
        description: 'Đảm bảo trình duyệt cho phép truy cập vị trí',
        actions: isMobile ? [
          'Nhấn vào biểu tượng khóa 🔒 bên cạnh URL',
          'Chọn "Vị trí" và bật "Cho phép"',
          isIOS ? 'Hoặc vào Cài đặt > Safari > Vị trí' : 'Hoặc vào Cài đặt > Ứng dụng > Chrome > Quyền'
        ] : [
          'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
          'Chọn "Cho phép" trong phần "Vị trí"',
          'Hoặc vào Cài đặt trình duyệt > Vị trí'
        ]
      }
    ]

    // Add mobile-specific troubleshooting steps
    if (isMobile) {
      commonSteps.push({
        icon: <MobileOutlined />,
        title: isIOS ? 'Cài đặt iOS' : 'Cài đặt Android',
        description: 'Kiểm tra cài đặt hệ thống cho ứng dụng trình duyệt',
        actions: isIOS ? [
          'Cài đặt > Quyền riêng tư & Bảo mật > Dịch vụ vị trí',
          'Bật "Dịch vụ vị trí"',
          'Cuộn xuống tìm "Safari" → "Khi sử dụng ứng dụng"',
          'Bật "Định vị chính xác" nếu có'
        ] : [
          'Cài đặt > Ứng dụng > Chrome (hoặc trình duyệt đang dùng)',
          'Quyền > Vị trí > "Cho phép chỉ khi sử dụng ứng dụng"',
          'Quay lại Cài đặt > Vị trí > Chế độ',
          'Chọn "Độ chính xác cao"'
        ]
      })
    }

    return commonSteps

    return steps
  }

  const getErrorMessage = () => {
    switch (errorCode) {
      case 1:
        return 'Quyền truy cập vị trí bị từ chối'
      case 2:
        return 'Không thể xác định vị trí hiện tại'
      case 3:
        return 'Hết thời gian chờ lấy vị trí'
      default:
        return 'Có lỗi xảy ra khi lấy vị trí'
    }
  }

  return (
    <Card 
      title={
        <Space>
          <EnvironmentOutlined />
          <span>Khắc phục lỗi lấy vị trí</span>
        </Space>
      }
      extra={<Button type="text" onClick={onClose}>✕</Button>}
      style={{ marginBottom: 16 }}
    >
      <Alert
        message={getErrorMessage()}
        description="Hãy thử các giải pháp bên dưới để khắc phục vấn đề."
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <List
        dataSource={getTroubleshootSteps()}
        renderItem={(step, index) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <Space align="start" style={{ width: '100%', marginBottom: 8 }}>
                {step.icon}
                <div style={{ flex: 1 }}>
                  <Text strong>{step.title}</Text>
                  <br />
                  <Text type="secondary">{step.description}</Text>
                </div>
              </Space>
              
              <List
                size="small"
                dataSource={step.actions}
                renderItem={(action) => (
                  <List.Item style={{ padding: '4px 0' }}>
                    <Text>• {action}</Text>
                  </List.Item>
                )}
                style={{ marginLeft: 24, marginTop: 8 }}
              />
            </div>
          </List.Item>
        )}
      />

      <Alert
        message="Lưu ý"
        description="Sau khi thực hiện các bước trên, hãy thử lại chức năng lấy vị trí. Nếu vẫn gặp lỗi, có thể do thiết bị không hỗ trợ GPS hoặc đang ở khu vực có tín hiệu yếu."
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Card>
  )
}

export default LocationTroubleshoot 