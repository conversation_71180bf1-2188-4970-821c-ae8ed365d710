import React, { useState } from 'react'
import { Typo<PERSON>, Card, Row, Col, Switch, But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form, Input, Space, Tag, message, Tooltip } from 'antd'
import {
  MailOutlined,
  PhoneOutlined,
  SecurityScanOutlined,
  KeyOutlined,
  EyeOutlined,
  Check<PERSON>ircleOutlined,
  InfoCircleOutlined,
  EnvironmentOutlined,
  QuestionCircleOutlined,
  GlobalOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import ChangePasswordModal from './ChangePasswordModal'
import SessionsModal from './SessionsModal'
import LocationPermissionGuide from './LocationPermissionGuide'
import LocationTroubleshoot from './LocationTroubleshoot'
import styles from '../../Profile.module.scss'

const { Text, Title } = Typography

interface UserData {
  id: string
  email: string
  gender: string
  fullName: string
  dateOfBirth: string
  citizenId: string
  phone: string
  address: string
  points: number
  qrCode: string
}

interface SecurityTabProps {
  is2FAEnabled: boolean
  showOTPSetup: boolean
  showDisableModal: boolean
  showChangePasswordModal: boolean
  showSessionsModal: boolean
  otpEmail: string
  otpCode: string
  disableOtpCode: string
  disableOtpSent: boolean
  otpSent: boolean
  isProcessing: boolean
  userData: UserData
  styles: any
  currentLocation: {latitude: number, longitude: number} | null
  isGettingLocation: boolean
  locationError: number | null
  handle2FAToggle: (checked: boolean) => void
  handleSendOTP: () => void
  handleVerifyOTP: () => void
  handleCancelOTPSetup: () => void
  handleSendDisableOTP: () => void
  handleDisable2FA: () => void
  handleCancelDisable: () => void
  handleShowChangePassword: () => void
  handleCancelChangePassword: () => void
  handleShowSessions: () => void
  handleCancelSessions: () => void
  handleOtpEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleDisableOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleGetCurrentLocation: () => Promise<void>
}

const SecurityTab: React.FC<SecurityTabProps> = ({
  is2FAEnabled,
  showOTPSetup,
  showDisableModal,
  showChangePasswordModal,
  showSessionsModal,
  otpEmail,
  otpCode,
  disableOtpCode,
  disableOtpSent,
  otpSent,
  isProcessing,
  userData,
  styles,
  currentLocation,
  isGettingLocation,
  locationError: propLocationError,
  handle2FAToggle,
  handleSendOTP,
  handleVerifyOTP,
  handleCancelOTPSetup,
  handleSendDisableOTP,
  handleDisable2FA,
  handleCancelDisable,
  handleShowChangePassword,
  handleCancelChangePassword,
  handleShowSessions,
  handleCancelSessions,
  handleOtpEmailChange,
  handleOtpCodeChange,
  handleDisableOtpCodeChange,
  handleGetCurrentLocation,
}) => {
  // Local state for location guides
  const [showLocationGuide, setShowLocationGuide] = useState(false)
  const [showLocationTroubleshoot, setShowLocationTroubleshoot] = useState(false)
  const [locationError, setLocationError] = useState<number | null>(null)
  const [showDebugInfo, setShowDebugInfo] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // Detect if user is on mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

  const handleLocationHelp = () => {
    setShowLocationGuide(true)
  }

  const handleLocationTroubleshoot = (errorCode?: number) => {
    setLocationError(errorCode || propLocationError || null)
    setShowLocationTroubleshoot(true)
  }

  const handleShowDebugInfo = async () => {
    try {
      const { getEnvironmentInfo, checkGeolocationPermission } = await import('@/utils/geolocation')
      const envInfo = getEnvironmentInfo()
      const permissionStatus = await checkGeolocationPermission()

      setDebugInfo({
        ...envInfo,
        permissionStatus
      })
      setShowDebugInfo(true)
    } catch (error) {
      console.error('Failed to get debug info:', error)
    }
  }
  return (
    <div className={styles.securityTab}>
      <div className={styles.securityOverview}>
        <Title level={4}>Tổng quan bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <MailOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Email</Text>
                  <Text type="secondary">{userData.email}</Text>
                  <Tag color="green" icon={<CheckCircleOutlined />}>
                    Đã xác thực
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <PhoneOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Số điện thoại</Text>
                  <Text type="secondary">
                    {userData.phone || 'Chưa cập nhật'}
                  </Text>
                  <Tag color={userData.phone ? "green" : "orange"} icon={userData.phone ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {userData.phone ? 'Đã xác thực' : 'Chưa cập nhật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <SecurityScanOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Xác thực 2 yếu tố</Text>
                  <Text type="secondary">
                    {is2FAEnabled ? 'Đã bật' : 'Chưa bật'}
                  </Text>
                  <Tag color={is2FAEnabled ? 'green' : 'orange'} icon={is2FAEnabled ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {is2FAEnabled ? 'Bảo mật cao' : 'Cần bật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <div className={styles.securityActions}>
        <Title level={4}>Hành động bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <KeyOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Đổi mật khẩu</Title>
                  <Text type="secondary">Cập nhật mật khẩu định kỳ để bảo mật tài khoản</Text>
                </div>
              </div>
              <Button type="primary" onClick={handleShowChangePassword} className={styles.actionButton}>
                Đổi mật khẩu
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <SecurityScanOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Xác thực 2 yếu tố</Title>
                  <Text type="secondary">Bật 2FA để tăng cường bảo mật tài khoản</Text>
                </div>
              </div>
              <Button 
                type={is2FAEnabled ? "default" : "primary"}
                onClick={() => handle2FAToggle(!is2FAEnabled)}
                className={styles.actionButton}
              >
                {is2FAEnabled ? 'Tắt 2FA' : 'Bật 2FA'}
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <EyeOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Phiên đăng nhập</Title>
                  <Text type="secondary">Xem các thiết bị đang đăng nhập</Text>
                </div>
              </div>
              <Button onClick={handleShowSessions} className={styles.actionButton}>
                Xem phiên
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <EnvironmentOutlined className={styles.actionIcon} />
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                    <Title level={5} style={{ margin: 0 }}>Lấy vị trí hiện tại</Title>
                    <Tooltip title="Hướng dẫn cấp quyền truy cập vị trí">
                      <Button
                        type="text"
                        size="small"
                        icon={<QuestionCircleOutlined />}
                        onClick={handleLocationHelp}
                        style={{ padding: '0 4px' }}
                      />
                    </Tooltip>
                  </div>
                  <Text type="secondary" style={{ fontSize: isMobile ? '13px' : '14px' }}>
                    {currentLocation
                      ? (
                          <div>
                            <div>Vĩ độ: {currentLocation.latitude.toFixed(6)}</div>
                            <div>Kinh độ: {currentLocation.longitude.toFixed(6)}</div>
                            <Button
                              type="link"
                              size="small"
                              icon={<GlobalOutlined />}
                              onClick={() => {
                                const mapsUrl = `https://www.google.com/maps?q=${currentLocation.latitude},${currentLocation.longitude}&z=16`
                                window.open(mapsUrl, '_blank')
                              }}
                              style={{ padding: '0', height: 'auto', fontSize: '12px' }}
                            >
                              Xem trên bản đồ
                            </Button>
                          </div>
                        )
                      : 'Xem vị trí hiện tại của bạn'
                    }
                  </Text>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                <Button
                  onClick={handleGetCurrentLocation}
                  loading={isGettingLocation}
                  className={styles.actionButton}
                  type={currentLocation ? "default" : "primary"}
                  style={{
                    minHeight: isMobile ? '44px' : '40px', // Better touch target on mobile
                    flex: 1,
                    minWidth: '120px'
                  }}
                >
                  {isGettingLocation
                    ? 'Đang lấy vị trí...'
                    : currentLocation
                      ? 'Lấy lại vị trí'
                      : 'Lấy vị trí'
                  }
                </Button>
                {!currentLocation && (
                  <Button
                    type="default"
                    size={isMobile ? "middle" : "small"}
                    onClick={() => handleLocationTroubleshoot()}
                    style={{
                      minHeight: isMobile ? '44px' : '40px',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Gặp lỗi?
                  </Button>
                )}
                <Button
                  type="default"
                  size="small"
                  onClick={handleShowDebugInfo}
                  style={{
                    minHeight: isMobile ? '44px' : '40px',
                    whiteSpace: 'nowrap',
                    fontSize: '11px'
                  }}
                >
                  Debug
                </Button>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Modals */}
      <ChangePasswordModal
        open={showChangePasswordModal}
        onCancel={handleCancelChangePassword}
        onSuccess={handleCancelChangePassword}
        isProcessing={isProcessing}
        styles={styles}
      />
      <SessionsModal open={showSessionsModal} onCancel={handleCancelSessions} styles={styles} />

      {/* Location Permission Guide */}
      {showLocationGuide && (
        <LocationPermissionGuide onClose={() => setShowLocationGuide(false)} />
      )}

      {/* Location Troubleshoot Guide */}
      {showLocationTroubleshoot && (
        <LocationTroubleshoot
          onClose={() => setShowLocationTroubleshoot(false)}
          errorCode={locationError}
        />
      )}

      {/* Debug Info Modal */}
      <Modal
        title="🔍 Geolocation Debug Information"
        open={showDebugInfo}
        onCancel={() => setShowDebugInfo(false)}
        footer={[
          <Button key="close" onClick={() => setShowDebugInfo(false)}>
            Đóng
          </Button>
        ]}
        width={800}
      >
        {debugInfo && (
          <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Thông tin này giúp debug lỗi geolocation"
                type="info"
                showIcon
              />

              <Card size="small" title="🌐 Browser & Device">
                <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                  <div><strong>User Agent:</strong> {debugInfo.userAgent}</div>
                  <div><strong>Platform:</strong> {debugInfo.platform}</div>
                  <div><strong>Language:</strong> {debugInfo.language}</div>
                  <div><strong>Online:</strong> {debugInfo.onLine ? 'Yes' : 'No'}</div>
                  <div><strong>Mobile:</strong> {debugInfo.isMobile ? 'Yes' : 'No'}</div>
                  <div><strong>iOS:</strong> {debugInfo.isIOS ? 'Yes' : 'No'}</div>
                  <div><strong>Android:</strong> {debugInfo.isAndroid ? 'Yes' : 'No'}</div>
                </div>
              </Card>

              <Card size="small" title="📍 Geolocation">
                <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                  <div><strong>Supported:</strong> {debugInfo.geolocationSupported ? 'Yes' : 'No'}</div>
                  <div><strong>Permission Status:</strong> {debugInfo.permissionStatus}</div>
                  <div><strong>Permissions API:</strong> {debugInfo.permissionsSupported ? 'Yes' : 'No'}</div>
                </div>
              </Card>

              <Card size="small" title="🌐 Connection & Location">
                <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                  <div><strong>Protocol:</strong> {debugInfo.protocol}</div>
                  <div><strong>Hostname:</strong> {debugInfo.hostname}</div>
                  <div><strong>Timezone:</strong> {debugInfo.timezone}</div>
                  <div><strong>Screen:</strong> {debugInfo.screenWidth}x{debugInfo.screenHeight}</div>
                  {debugInfo.connection && (
                    <div><strong>Connection:</strong> {JSON.stringify(debugInfo.connection, null, 2)}</div>
                  )}
                </div>
              </Card>

              <Card size="small" title="📋 Raw Data">
                <pre style={{
                  fontSize: '10px',
                  background: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  maxHeight: '200px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </Card>
            </Space>
          </div>
        )}
      </Modal>

      {/* 2FA Setup Modal */}
      <Modal
        title="Thiết lập xác thực 2 yếu tố"
        open={showOTPSetup}
        onCancel={handleCancelOTPSetup}
        footer={null}
      >
        <div className={styles.otpSetup}>
          <Alert
            message="Bảo mật tài khoản"
            description="Xác thực 2 yếu tố giúp bảo vệ tài khoản của bạn khỏi truy cập trái phép."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Form layout="vertical">
            <Form.Item label="Email nhận mã OTP">
              <Input
                value={otpEmail}
                onChange={handleOtpEmailChange}
                placeholder="Nhập email"
              />
            </Form.Item>
            {otpSent && (
              <Form.Item label="Mã OTP">
                <Input
                  value={otpCode}
                  onChange={handleOtpCodeChange}
                  placeholder="Nhập mã OTP"
                />
              </Form.Item>
            )}
            <Space>
              {!otpSent ? (
                <Button
                  type="primary"
                  onClick={handleSendOTP}
                  loading={isProcessing}
                >
                  Gửi mã OTP
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={handleVerifyOTP}
                  loading={isProcessing}
                >
                  Xác thực
                </Button>
              )}
              <Button onClick={handleCancelOTPSetup}>Hủy</Button>
            </Space>
          </Form>
        </div>
      </Modal>

      {/* Disable 2FA Modal */}
      <Modal
        title="Tắt xác thực 2 yếu tố"
        open={showDisableModal}
        onCancel={handleCancelDisable}
        footer={null}
      >
        <div className={styles.otpSetup}>
          <Alert
            message="Cảnh báo bảo mật"
            description="Tắt xác thực 2 yếu tố sẽ làm giảm mức độ bảo mật của tài khoản."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Form layout="vertical">
            {!disableOtpSent ? (
              <Button
                type="primary"
                danger
                onClick={handleSendDisableOTP}
                loading={isProcessing}
              >
                Gửi mã OTP xác nhận
              </Button>
            ) : (
              <>
                <Form.Item label="Mã OTP xác nhận">
                  <Input
                    value={disableOtpCode}
                    onChange={handleDisableOtpCodeChange}
                    placeholder="Nhập mã OTP"
                  />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    danger
                    onClick={handleDisable2FA}
                    loading={isProcessing}
                  >
                    Tắt 2FA
                  </Button>
                  <Button onClick={handleCancelDisable}>Hủy</Button>
                </Space>
              </>
            )}
          </Form>
        </div>
      </Modal>

      {/* Location Permission Guide */}
      {/* Location Troubleshoot */}
    </div>
  )
}

export default SecurityTab 