/**
 * Geolocation utility functions for cross-platform location services
 */

export interface LocationResult {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: number
}

export interface GeolocationError {
  code: number
  message: string
  name: string
}

/**
 * Check if geolocation is supported by the browser
 */
export const isGeolocationSupported = (): boolean => {
  return 'geolocation' in navigator
}

/**
 * Get device type information
 */
export const getDeviceInfo = () => {
  const userAgent = navigator.userAgent
  return {
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isDesktop: !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  }
}

/**
 * Get optimal geolocation options based on device type
 */
export const getGeolocationOptions = (): PositionOptions => {
  const deviceInfo = getDeviceInfo()
  
  return {
    enableHighAccuracy: true, // Always use high accuracy
    timeout: deviceInfo.isMobile ? 20000 : 15000, // Longer timeout for mobile
    maximumAge: deviceInfo.isMobile ? 600000 : 300000 // 10 min for mobile, 5 min for desktop
  }
}

/**
 * Get current location with enhanced error handling
 */
export const getCurrentLocation = (): Promise<LocationResult> => {
  return new Promise((resolve, reject) => {
    // Check support
    if (!isGeolocationSupported()) {
      reject(new Error('Trình duyệt không hỗ trợ định vị GPS'))
      return
    }

    const options = getGeolocationOptions()
    const deviceInfo = getDeviceInfo()

    console.log('🌍 Starting geolocation request...', {
      device: deviceInfo,
      options
    })

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const result: LocationResult = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        }

        console.log('✅ Geolocation success:', result)
        resolve(result)
      },
      (error) => {
        console.error('❌ Geolocation error:', error)
        
        const enhancedError: GeolocationError = {
          code: error.code,
          message: getErrorMessage(error.code),
          name: error.name
        }

        reject(enhancedError)
      },
      options
    )
  })
}

/**
 * Get user-friendly error messages
 */
export const getErrorMessage = (errorCode: number): string => {
  switch (errorCode) {
    case GeolocationPositionError.PERMISSION_DENIED:
      return 'Bạn đã từ chối quyền truy cập vị trí'
    case GeolocationPositionError.POSITION_UNAVAILABLE:
      return 'Không thể xác định vị trí hiện tại'
    case GeolocationPositionError.TIMEOUT:
      return 'Quá thời gian chờ lấy vị trí'
    default:
      return 'Lỗi không xác định khi lấy vị trí'
  }
}

/**
 * Get troubleshooting tips based on error code and device
 */
export const getTroubleshootingTips = (errorCode: number): string[] => {
  const deviceInfo = getDeviceInfo()
  
  switch (errorCode) {
    case GeolocationPositionError.PERMISSION_DENIED:
      if (deviceInfo.isMobile) {
        return [
          'Kiểm tra cài đặt quyền ứng dụng trình duyệt',
          'Bật dịch vụ vị trí trong cài đặt thiết bị',
          'Thử khởi động lại ứng dụng trình duyệt'
        ]
      }
      return [
        'Nhấn vào biểu tượng khóa ở thanh địa chỉ',
        'Chọn "Cho phép" trong phần vị trí',
        'Khởi động lại trình duyệt nếu cần'
      ]
      
    case GeolocationPositionError.POSITION_UNAVAILABLE:
      return [
        'Kiểm tra kết nối internet',
        'Bật GPS/dịch vụ vị trí',
        'Di chuyển ra ngoài trời nếu đang ở trong nhà'
      ]
      
    case GeolocationPositionError.TIMEOUT:
      return [
        'Thử lại sau vài giây',
        'Di chuyển ra ngoài trời',
        'Kiểm tra tín hiệu GPS'
      ]
      
    default:
      return [
        'Kiểm tra kết nối mạng',
        'Khởi động lại trình duyệt',
        'Thử trên thiết bị khác'
      ]
  }
}

/**
 * Format coordinates for display
 */
export const formatCoordinates = (lat: number, lng: number, precision: number = 6): string => {
  return `${lat.toFixed(precision)}, ${lng.toFixed(precision)}`
}

/**
 * Generate Google Maps URL
 */
export const getGoogleMapsUrl = (lat: number, lng: number, zoom: number = 16): string => {
  return `https://www.google.com/maps?q=${lat},${lng}&z=${zoom}`
}

/**
 * Calculate accuracy description
 */
export const getAccuracyDescription = (accuracy: number): string => {
  if (accuracy < 10) return 'rất cao'
  if (accuracy < 50) return 'cao'
  if (accuracy < 100) return 'trung bình'
  if (accuracy < 1000) return 'thấp'
  return 'rất thấp'
}
