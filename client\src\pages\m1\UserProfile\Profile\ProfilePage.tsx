import React, { useEffect, useState, useCallback } from 'react'
import { Tabs, Form } from 'antd'
import { motion } from 'framer-motion'
import styles from './Profile.module.scss'

// Import components
import { HeroSection, PersonalInfoTab, AffiliateTab, SecurityTab } from './components'

// Import hooks
import { useGetUserProfileHook, useUpdateAvatarHook } from '@/hooks/user'
import {
  useGetAffiliateMeHook,
  useGetAffiliateTreeHook,
  useGetAffiliateStatsHook,
  useGetAffiliateDirectHook,
  useGetAffiliateIndirectHook,
} from '@/hooks/affiliate/useAffiliate'
import {
  useRequest2FAOTP,
  useVerify2FAOTP,
  useDisable2FA,
} from '@/hooks/auth/useLogin'

// Import utilities
import { useToast } from '@/components/Toast'

const { TabPane } = Tabs

// Interfaces
interface UserData {
  id: string
  email: string
  gender: string
  fullName: string
  dateOfBirth: string
  citizenId: string
  phone: string
  address: string
  points: number
  qrCode: string
}

const ProfilePage = () => {
  // Form
  const [form] = Form.useForm()
  const [isEditing, setIsEditing] = useState(false)
  const [showOtpEmail, setShowOtpEmail] = useState(false)

  // 2FA States
  const [is2FAEnabled, setIs2FAEnabled] = useState(false)
  const [showOTPSetup, setShowOTPSetup] = useState(false)
  const [showDisableModal, setShowDisableModal] = useState(false)
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false)
  const [showSessionsModal, setShowSessionsModal] = useState(false)
  const [otpEmail, setOtpEmail] = useState('')
  const [otpCode, setOtpCode] = useState('')
  const [disableOtpCode, setDisableOtpCode] = useState('')
  const [disableOtpSent, setDisableOtpSent] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // Location state
  const [currentLocation, setCurrentLocation] = useState<{latitude: number, longitude: number} | null>(null)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [locationError, setLocationError] = useState<number | null>(null)

  // Hooks
  const { data: profileData, refetch: refetchProfile } = useGetUserProfileHook()
  const updateAvatar = useUpdateAvatarHook()
  const { showSuccess, showError } = useToast()

  // Create userData from profileData
  const userData = profileData ? {
    id: profileData.id || '',
    email: profileData.email || '',
    gender: profileData.gender || '',
    fullName: profileData.fullName || '',
    dateOfBirth: profileData.dateOfBirth || '',
    citizenId: profileData.citizenId || '',
    phone: profileData.phoneNumber || '',
    address: profileData.address || '',
    points: 0,
    qrCode: profileData.uIdCode || '',
  } : null

  // Affiliate hooks
  const { data: affiliateMe } = useGetAffiliateMeHook()
  const { data: affiliateTree } = useGetAffiliateTreeHook()
  const { data: affiliateStats } = useGetAffiliateStatsHook()
  const { data: affiliateDirect } = useGetAffiliateDirectHook()
  const { data: affiliateIndirect } = useGetAffiliateIndirectHook(2) // Add default level

  // 2FA hooks
  const requestOTP = useRequest2FAOTP()
  const verifyOTP = useVerify2FAOTP()
  const { sendOTP: sendDisableOTP, disable2FA } = useDisable2FA()

  // Avatar upload handler
  const handleAvatarUpload = async (file: File, previewUrl: string) => {
    try {
      const success = await updateAvatar(file)
      if (success) {
        showSuccess('Cập nhật ảnh đại diện thành công')
        refetchProfile()
      }
    } catch (error) {
      console.error('Avatar upload error:', error)
      showError('Cập nhật ảnh đại diện thất bại')
    }
  }

  // 2FA handlers
  const handle2FAToggle = useCallback((checked: boolean) => {
    if (!userData) return
    if (checked) {
      setShowOTPSetup(true)
      setOtpEmail(userData.email)
    } else {
      setShowDisableModal(true)
    }
  }, [userData?.email])

  const handleSendOTP = useCallback(async () => {
    if (!otpEmail) return

    setIsProcessing(true)
    const success = await requestOTP(otpEmail)

    if (success) {
      setOtpSent(true)
    }
    setIsProcessing(false)
  }, [otpEmail, requestOTP])

  const handleVerifyOTP = useCallback(async () => {
    if (!otpCode) return

    setIsProcessing(true)
    const success = await verifyOTP(otpEmail, otpCode)

    if (success) {
      setIs2FAEnabled(true)
      setShowOTPSetup(false)
      setOtpSent(false)
      setOtpCode('')
    }
    setIsProcessing(false)
  }, [otpEmail, otpCode, verifyOTP])

  const handleCancelOTPSetup = useCallback(() => {
    setShowOTPSetup(false)
    setOtpSent(false)
    setOtpCode('')
  }, [])

  const handleSendDisableOTP = useCallback(async () => {
    if (!userData) return
    setIsProcessing(true)
    const success = await sendDisableOTP(userData.email)

    if (success) {
      setDisableOtpSent(true)
    }
    setIsProcessing(false)
  }, [userData?.email, sendDisableOTP])

  const handleDisable2FA = useCallback(async () => {
    if (!disableOtpCode || !userData) return

    setIsProcessing(true)
    const success = await disable2FA(userData.email, disableOtpCode)

    if (success) {
      setIs2FAEnabled(false)
      setShowDisableModal(false)
      setDisableOtpSent(false)
      setDisableOtpCode('')
    }
    setIsProcessing(false)
  }, [userData?.email, disableOtpCode, disable2FA])

  const handleCancelDisable = useCallback(() => {
    setShowDisableModal(false)
    setDisableOtpSent(false)
    setDisableOtpCode('')
  }, [])

  const handleShowChangePassword = useCallback(() => {
    setShowChangePasswordModal(true)
  }, [])

  const handleCancelChangePassword = useCallback(() => {
    setShowChangePasswordModal(false)
  }, [])

  const handleShowSessions = useCallback(async () => {
    setShowSessionsModal(true)
  }, [])

  const handleCancelSessions = useCallback(() => {
    setShowSessionsModal(false)
  }, [])

  const handleOtpEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpEmail(e.target.value)
  }, [])

  const handleOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpCode(e.target.value)
  }, [])

  const handleDisableOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDisableOtpCode(e.target.value)
  }, [])

  // Location handler with real geolocation using utility
  const handleGetCurrentLocation = useCallback(async () => {
    setIsGettingLocation(true)
    setLocationError(null) // Reset previous errors

    try {
      // Import the geolocation utility
      const { getCurrentLocation, getAccuracyDescription, getGoogleMapsUrl } = await import('@/utils/geolocation')

      // Get current location
      const result = await getCurrentLocation()

      setCurrentLocation({
        latitude: result.latitude,
        longitude: result.longitude
      })

      // Show success message with accuracy info
      const accuracyText = getAccuracyDescription(result.accuracy)

      showSuccess(
        `Đã lấy vị trí thành công! Vĩ độ: ${result.latitude.toFixed(6)}, Kinh độ: ${result.longitude.toFixed(6)} (độ chính xác ${accuracyText})`
      )

      // Auto-open Google Maps with the location
      const mapsUrl = getGoogleMapsUrl(result.latitude, result.longitude)
      window.open(mapsUrl, '_blank')

    } catch (error: any) {
      console.error('Geolocation error:', error)

      // Store error code for troubleshooting
      if (error.code) {
        setLocationError(error.code)
      } else {
        setLocationError(0) // Generic error
      }

      // Show user-friendly error message
      const errorMessage = error.message || 'Không thể lấy vị trí hiện tại'
      showError(errorMessage)

    } finally {
      setIsGettingLocation(false)
    }
  }, [showSuccess, showError])

  if (!profileData || !userData) {
    return <div>Loading...</div>
  }

  return (
    <div className={styles.profilePage}>
      {/* Hero Section */}
      <HeroSection
        profileData={profileData}
        userData={userData}
        affiliateStats={affiliateStats}
        onAvatarUpload={handleAvatarUpload}
      />

      {/* Main Content */}
      <section className={styles.mainSection}>
        <div className={styles.container}>
          <Tabs defaultActiveKey="1" className={styles.profileTabs}>
            <TabPane tab="Thông tin cá nhân" key="1">
              <PersonalInfoTab
                userData={userData}
                profileData={profileData}
                form={form}
                isEditing={isEditing}
                setIsEditing={setIsEditing}
                showOtpEmail={showOtpEmail}
                setShowOtpEmail={setShowOtpEmail}
              />
            </TabPane>

            <TabPane tab="Affiliate" key="2">
              <AffiliateTab
                affiliateStats={affiliateStats}
                affiliateMe={affiliateMe}
                affiliateTree={affiliateTree}
                affiliateDirect={affiliateDirect}
                affiliateIndirect={affiliateIndirect}
              />
            </TabPane>

            <TabPane tab="Bảo mật" key="3">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <SecurityTab
                  is2FAEnabled={is2FAEnabled}
                  showOTPSetup={showOTPSetup}
                  showDisableModal={showDisableModal}
                  showChangePasswordModal={showChangePasswordModal}
                  showSessionsModal={showSessionsModal}
                  otpEmail={otpEmail}
                  otpCode={otpCode}
                  disableOtpCode={disableOtpCode}
                  disableOtpSent={disableOtpSent}
                  otpSent={otpSent}
                  isProcessing={isProcessing}
                  userData={userData}
                  styles={styles}
                  currentLocation={currentLocation}
                  isGettingLocation={isGettingLocation}
                  locationError={locationError}
                  handle2FAToggle={handle2FAToggle}
                  handleSendOTP={handleSendOTP}
                  handleVerifyOTP={handleVerifyOTP}
                  handleCancelOTPSetup={handleCancelOTPSetup}
                  handleSendDisableOTP={handleSendDisableOTP}
                  handleDisable2FA={handleDisable2FA}
                  handleCancelDisable={handleCancelDisable}
                  handleShowChangePassword={handleShowChangePassword}
                  handleCancelChangePassword={handleCancelChangePassword}
                  handleShowSessions={handleShowSessions}
                  handleCancelSessions={handleCancelSessions}
                  handleOtpEmailChange={handleOtpEmailChange}
                  handleOtpCodeChange={handleOtpCodeChange}
                  handleDisableOtpCodeChange={handleDisableOtpCodeChange}
                  handleGetCurrentLocation={handleGetCurrentLocation}
                />
              </motion.div>
            </TabPane>
          </Tabs>
        </div>
      </section>
    </div>
  )
}

export default ProfilePage 