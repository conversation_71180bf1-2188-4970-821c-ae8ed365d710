/**
 * Geolocation utility functions for cross-platform location services
 */

export interface LocationResult {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: number
}

export interface GeolocationError {
  code: number
  message: string
  name: string
}

/**
 * Check if geolocation is supported by the browser
 */
export const isGeolocationSupported = (): boolean => {
  return 'geolocation' in navigator
}

/**
 * Get device type information
 */
export const getDeviceInfo = () => {
  const userAgent = navigator.userAgent
  return {
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isDesktop: !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  }
}

/**
 * Get optimal geolocation options based on device type and attempt number
 */
export const getGeolocationOptions = (attempt: number = 1): PositionOptions => {
  const deviceInfo = getDeviceInfo()

  // First attempt: High accuracy with GPS
  if (attempt === 1) {
    return {
      enableHighAccuracy: true, // Use GPS
      timeout: deviceInfo.isMobile ? 25000 : 20000, // Longer timeout
      maximumAge: 60000 // Accept 1-minute old position
    }
  }

  // Second attempt: Lower accuracy, faster response
  return {
    enableHighAccuracy: false, // Use network-based location
    timeout: 10000, // Shorter timeout
    maximumAge: 300000 // Accept 5-minute old position
  }
}

/**
 * Get current location with enhanced error handling and fallback
 */
export const getCurrentLocation = (): Promise<LocationResult> => {
  return new Promise((resolve, reject) => {
    // Check support
    if (!isGeolocationSupported()) {
      reject(new Error('Trình duyệt không hỗ trợ định vị GPS'))
      return
    }

    const deviceInfo = getDeviceInfo()
    let attemptCount = 0

    const tryGetLocation = (attempt: number) => {
      attemptCount++
      const options = getGeolocationOptions(attempt)

      console.log(`🌍 Geolocation attempt ${attempt}...`, {
        device: deviceInfo,
        options,
        userAgent: navigator.userAgent.substring(0, 100)
      })

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const result: LocationResult = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          }

          console.log('✅ Geolocation success:', {
            ...result,
            attempt,
            accuracyLevel: getAccuracyDescription(result.accuracy)
          })
          resolve(result)
        },
        (error) => {
          console.error(`❌ Geolocation attempt ${attempt} failed:`, {
            code: error.code,
            message: error.message,
            attempt,
            options
          })

          // Try fallback on first attempt failure
          if (attempt === 1 && error.code === GeolocationPositionError.POSITION_UNAVAILABLE) {
            console.log('🔄 Trying fallback with lower accuracy...')
            setTimeout(() => tryGetLocation(2), 1000)
            return
          }

          // Final failure
          const enhancedError: GeolocationError = {
            code: error.code,
            message: getErrorMessage(error.code),
            name: error.name
          }

          reject(enhancedError)
        },
        options
      )
    }

    // Start with first attempt
    tryGetLocation(1)
  })
}

/**
 * Get user-friendly error messages with detailed explanations
 */
export const getErrorMessage = (errorCode: number): string => {
  const deviceInfo = getDeviceInfo()

  switch (errorCode) {
    case GeolocationPositionError.PERMISSION_DENIED:
      return 'Bạn đã từ chối quyền truy cập vị trí. Vui lòng cấp quyền trong cài đặt trình duyệt.'
    case GeolocationPositionError.POSITION_UNAVAILABLE:
      if (deviceInfo.isMobile) {
        return 'Không thể xác định vị trí. Hãy bật GPS, kiểm tra kết nối mạng và thử di chuyển ra ngoài trời.'
      }
      return 'Không thể xác định vị trí. Hãy kiểm tra kết nối mạng và thử lại.'
    case GeolocationPositionError.TIMEOUT:
      return 'Quá thời gian chờ lấy vị trí. Hãy thử lại hoặc di chuyển ra ngoài trời.'
    default:
      return 'Lỗi không xác định khi lấy vị trí. Hãy thử lại sau.'
  }
}

/**
 * Get troubleshooting tips based on error code and device
 */
export const getTroubleshootingTips = (errorCode: number): string[] => {
  const deviceInfo = getDeviceInfo()
  
  switch (errorCode) {
    case GeolocationPositionError.PERMISSION_DENIED:
      if (deviceInfo.isMobile) {
        return [
          'Kiểm tra cài đặt quyền ứng dụng trình duyệt',
          'Bật dịch vụ vị trí trong cài đặt thiết bị',
          'Thử khởi động lại ứng dụng trình duyệt'
        ]
      }
      return [
        'Nhấn vào biểu tượng khóa ở thanh địa chỉ',
        'Chọn "Cho phép" trong phần vị trí',
        'Khởi động lại trình duyệt nếu cần'
      ]
      
    case GeolocationPositionError.POSITION_UNAVAILABLE:
      return [
        'Kiểm tra kết nối internet',
        'Bật GPS/dịch vụ vị trí',
        'Di chuyển ra ngoài trời nếu đang ở trong nhà'
      ]
      
    case GeolocationPositionError.TIMEOUT:
      return [
        'Thử lại sau vài giây',
        'Di chuyển ra ngoài trời',
        'Kiểm tra tín hiệu GPS'
      ]
      
    default:
      return [
        'Kiểm tra kết nối mạng',
        'Khởi động lại trình duyệt',
        'Thử trên thiết bị khác'
      ]
  }
}

/**
 * Format coordinates for display
 */
export const formatCoordinates = (lat: number, lng: number, precision: number = 6): string => {
  return `${lat.toFixed(precision)}, ${lng.toFixed(precision)}`
}

/**
 * Generate Google Maps URL
 */
export const getGoogleMapsUrl = (lat: number, lng: number, zoom: number = 16): string => {
  return `https://www.google.com/maps?q=${lat},${lng}&z=${zoom}`
}

/**
 * Calculate accuracy description
 */
export const getAccuracyDescription = (accuracy: number): string => {
  if (accuracy < 10) return 'rất cao'
  if (accuracy < 50) return 'cao'
  if (accuracy < 100) return 'trung bình'
  if (accuracy < 1000) return 'thấp'
  return 'rất thấp'
}

/**
 * Get detailed environment information for debugging
 */
export const getEnvironmentInfo = () => {
  const deviceInfo = getDeviceInfo()

  return {
    // Browser info
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,

    // Geolocation support
    geolocationSupported: isGeolocationSupported(),

    // Device info
    ...deviceInfo,

    // Connection info
    onLine: navigator.onLine,
    connection: (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection,

    // Location info
    protocol: window.location.protocol,
    hostname: window.location.hostname,

    // Permissions API support
    permissionsSupported: 'permissions' in navigator,

    // Screen info
    screenWidth: screen.width,
    screenHeight: screen.height,

    // Time info
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: new Date().toISOString()
  }
}

/**
 * Check geolocation permission status (if supported)
 */
export const checkGeolocationPermission = async (): Promise<string> => {
  if (!('permissions' in navigator)) {
    return 'Permissions API không được hỗ trợ'
  }

  try {
    const permission = await navigator.permissions.query({ name: 'geolocation' as PermissionName })
    return permission.state // 'granted', 'denied', 'prompt'
  } catch (error) {
    return 'Không thể kiểm tra permission'
  }
}
