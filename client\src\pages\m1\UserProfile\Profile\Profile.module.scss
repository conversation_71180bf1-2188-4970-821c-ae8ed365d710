// ===== NEW PROFILE LAYOUT STYLES =====
.profilePage {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

// Hero Section
.heroSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 0;
  color: white;
  margin-bottom: 16px;
}

.heroContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
}

.heroLeft {
  display: flex;
  align-items: center;
  gap: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.avatarSection {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.heroAvatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.changeAvatarBtn {
  border-radius: 20px;
  height: 32px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}
}

.heroInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.heroName {
  color: white !important;
  margin: 0 !important;
  font-size: 2rem;
  font-weight: 700;
  
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

.heroRole {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 500;
}

.heroStats {
  display: flex;
  gap: 24px;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
}

.statItem {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  
  svg {
    font-size: 18px;
  }
}

.heroRight {
  @media (max-width: 768px) {
    order: -1;
  }
}

.qrSection {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Main Section
.mainSection {
  padding: 16px 0;
}

.profileTabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  :global(.ant-tabs-nav) {
    margin: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }
  
  :global(.ant-tabs-tab) {
    padding: 16px 24px;
    font-weight: 500;
    color: #6c757d;

  &:hover {
      color: #667eea;
    }
  }
  
  :global(.ant-tabs-tab-active) {
    color: #667eea !important;
    font-weight: 600;
  }
  
  :global(.ant-tabs-ink-bar) {
    background: #667eea;
    height: 3px;
  }
  
  :global(.ant-tabs-content-holder) {
    padding: 32px;
  }
}

// Personal Info Section
.personalInfoSection {
  .infoCard {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
  }
  
  .infoList {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .infoItem {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: white;
  border-radius: 8px;
    border-left: 3px solid #667eea;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateX(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    svg {
      font-size: 18px;
      color: #667eea;
      margin-top: 2px;
    }
    
    > div {
      flex: 1;
  display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
}

// Affiliate Section
.affiliateSection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.statsRow {
  margin-bottom: 24px;
}

.statCard {
  text-align: center;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

  :global(.ant-statistic-title) {
    color: #6c757d;
  font-weight: 500;
  }
  
  :global(.ant-statistic-content) {
    color: #667eea;
  font-weight: 600;
  }
}

.referralCard {
  border-radius: 12px;
  border: 1px solid #e9ecef;
  
  .referralLink {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
  }
  
  .linkText {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
  }
}

.treeCard, .listCard {
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.treeContainer {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
    border-radius: 8px;
}

.listHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

.affiliateList {
  .affiliateItem {
    padding: 16px;
  border-radius: 8px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
      transition: all 0.2s ease;
    
    &:hover {
  background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  .affiliateMeta {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 4px;
    }
  }
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  
  .emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}

// Security Section
.securityTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.securityOverview {
  .securityCard {
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .securityItem {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .securityIcon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }
  
  .securityInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
}

.securityActions {
  .actionCard {
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .actionHeader {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .actionIcon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 18px;
  }
  
  .actionButton {
    border-radius: 8px;
    height: 40px;
    font-weight: 500;
  }
  
  .actionSwitch {
    margin-top: 8px;
  }

  // Mobile optimizations for location feature
  @media (max-width: 768px) {
    .actionCard {
      padding: 16px;

      .ant-card-body {
        padding: 16px;
      }
    }

    .actionButton {
      height: 44px; // Better touch targets
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .actionHeader {
      gap: 12px;
      margin-bottom: 12px;
    }

    .actionIcon {
      width: 36px;
      height: 36px;
      font-size: 16px;
      flex-shrink: 0;
    }

    .actionButton {
      min-width: 100px;
    }
  }

  // Location-specific styles
  .locationInfo {
    font-size: 13px;
    line-height: 1.4;

    @media (max-width: 480px) {
      font-size: 12px;
    }
  }

  .locationButtons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    @media (max-width: 480px) {
      gap: 6px;
    }
  }
}

.otpSetup {
  .ant-form-item {
    margin-bottom: 16px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .heroSection {
    padding: 40px 0;
  }
  
  .mainSection {
    padding: 20px 0;
  }
  
  .profileTabs {
    :global(.ant-tabs-content-holder) {
      padding: 20px;
    }
  }
  
  .statsRow {
    .statCard {
      margin-bottom: 16px;
    }
  }
  
  .securityOverview {
    .securityItem {
      flex-direction: column;
      text-align: center;
      gap: 12px;
    }
  }
  
  .securityActions {
    .actionHeader {
      flex-direction: column;
      text-align: center;
    gap: 12px;
    }
  }
}

// Animation Classes
.fadeIn {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideIn {
  animation: slideIn 0.6s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Legacy styles for backward compatibility
.profileContainer {
  @extend .profilePage;
}

.profileContent {
  @extend .mainSection;
}

.profileSidebar {
  @extend .infoCard;
}

.mainContent {
  @extend .profileTabs;
}

.avatarContainer {
  @extend .avatarSection;
}

.avatar {
  @extend .heroAvatar;
}

.changeAvatarBtn {
  @extend .changeAvatarBtn;
}

.userInfo {
  @extend .heroInfo;
}

.userName {
  @extend .heroName;
}

.userDetails {
  @extend .heroStats;
}

.detailItem {
  @extend .infoItem;
}

.detailLabel {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.detailValue {
  font-size: 14px;
    color: #2e2e2e;
  font-weight: 500;
}

.qrSection {
  @extend .qrSection;
}

.affiliateContent {
  @extend .affiliateSection;
}

.affiliateTitle {
  @extend .heroName;
  color: #2e2e2e !important;
  font-size: 1.5rem !important;
}

.referralSection {
  @extend .referralCard;
}

.referralInfo {
  @extend .referralLink;
}

.referralLabel {
  @extend .detailLabel;
}

.referralValue {
  @extend .linkText;
}

.sectionTitle {
  @extend .heroName;
  color: #2e2e2e !important;
  font-size: 1.2rem !important;
  margin-bottom: 16px !important;
}

.treeContainer {
  @extend .treeContainer;
}

.affiliateTableContainer {
  @extend .affiliateList;
}

.levelSelector {
  @extend .listHeader;
}

.levelLabel {
  @extend .detailLabel;
}

.levelSelect {
  width: 120px;
}

.emptyState {
  @extend .emptyState;
}

.emptyText {
  @extend .emptyState;
}