// src/constants/sidebarItems.tsx
import {
  MdOutlinePeopleAlt,
  MdDashboard,
  MdPeople,
  MdInventory2,
  MdShoppingCart,
  MdBarChart,
  MdWarehouse,
  MdCategory,
  MdLocalShipping,
  MdAccountBalance,
  MdBusiness,
  MdAgriculture,
  MdAttachMoney,
  MdPolicy,
  MdEmojiEvents,
  MdAssessment,
  MdGroups,
  MdSmartToy,
} from 'react-icons/md'
import { ROUTES } from './routes'
import type { RoleType } from '@/services/M1/auth'
import type { Role } from '@/types/components/route'
import { FaUserLock } from 'react-icons/fa'

export type SiderItem = {
  name: string
  link: string
  icon: React.ReactNode
  requiredPermissions?: string[] // 👈 Thêm dòng này để dùng với checkPermission
}

export const commonSidebarItems: SiderItem[] = [
  {
    name: 'Trang chủ',
    link: ROUTES.HOME,
    icon: <MdOutlinePeopleAlt />,
    requiredPermissions: [],
  },
]

export const permissionSidebarItems: SiderItem[] = [
  {
    name: 'Bảng điều khiển',
    link: ROUTES.DASHBOARD,
    icon: <MdDashboard />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý quyền',
    link: ROUTES.PERMISSION_MANAGEMENT,
    icon: <FaUserLock />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý người dùng',
    link: ROUTES.USER_MANAGEMENT,
    icon: <MdPeople />,
    requiredPermissions: ['PERM_USER_LIST'],
  },
  {
    name: 'Quản lý sản phẩm',
    link: ROUTES.PRODUCT_MANAGEMENT,
    icon: <MdInventory2 />,
    requiredPermissions: ['PERM_PRODUCT_MANAGE'],
  },
  {
    name: 'Quản lý đơn hàng',
    link: ROUTES.ORDER_MANAGEMENT,
    icon: <MdShoppingCart />,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    name: 'Thống kê đơn hàng',
    link: ROUTES.ORDER_STATISTICS,
    icon: <MdBarChart />,
    requiredPermissions: ['PERM_ORDER_VIEW_ALL'],
  },
  {
    name: 'Quản lý hàng tồn kho',
    link: ROUTES.INVENTORY_MANAGEMENT,
    icon: <MdWarehouse />,
    requiredPermissions: ['PERM_INVENTORY_MANAGE'],
  },
  {
    name: 'Quản lý danh mục',
    link: ROUTES.CATEGORY_MANAGEMENT,
    icon: <MdCategory />,
    requiredPermissions: ['PERM_CATEGORY_MANAGE'],
  },
  // R06 - Tài xế
  {
    name: 'Tài xế',
    link: ROUTES.DRIVER_DASHBOARD,
    icon: <MdLocalShipping />,
    requiredPermissions: ['PERM_DRIVER_ACCESS'],
  },
  // R07 - Nhà đầu tư
  {
    name: 'Nhà đầu tư',
    link: ROUTES.INVESTOR_DASHBOARD,
    icon: <MdAccountBalance />,
    requiredPermissions: ['PERM_INVESTOR_ACCESS'],
  },
  // R08 - Ban điều hành
  {
    name: 'Ban điều hành',
    link: ROUTES.EXECUTIVE_DASHBOARD,
    icon: <MdBusiness />,
    requiredPermissions: ['PERM_EXECUTIVE_ACCESS'],
  },
  // R09 - Người phụ trách ngành
  {
    name: 'Phụ trách ngành',
    link: ROUTES.SECTOR_MANAGER,
    icon: <MdPolicy />,
    requiredPermissions: ['PERM_SECTOR_MANAGER_ACCESS'],
  },
  // R10 - Cán bộ nông nghiệp
  {
    name: 'Cán bộ nông nghiệp',
    link: ROUTES.AGRICULTURE_OFFICER,
    icon: <MdAgriculture />,
    requiredPermissions: ['PERM_AGRICULTURE_OFFICER_ACCESS'],
  },
  // R11 - Cán bộ thuế & tài chính
  {
    name: 'Cán bộ thuế & tài chính',
    link: ROUTES.TAX_FINANCE_OFFICER,
    icon: <MdAttachMoney />,
    requiredPermissions: ['PERM_TAX_FINANCE_OFFICER_ACCESS'],
  },
  // R12 - Cán bộ liên ngành
  {
    name: 'Cán bộ liên ngành',
    link: ROUTES.INTERSECTORAL_OFFICER,
    icon: <MdPolicy />,
    requiredPermissions: ['PERM_INTERSECTORAL_OFFICER_ACCESS'],
  },
  // R13 - Đại sứ TAP
  {
    name: 'Đại sứ TAP',
    link: ROUTES.TAP_AMBASSADOR,
    icon: <MdEmojiEvents />,
    requiredPermissions: ['PERM_TAP_AMBASSADOR_ACCESS'],
  },
  // R14 - Chuyên viên thẩm định
  {
    name: 'Chuyên viên thẩm định',
    link: ROUTES.ASSESSMENT_SPECIALIST,
    icon: <MdAssessment />,
    requiredPermissions: ['PERM_ASSESSMENT_SPECIALIST_ACCESS'],
  },
  // R15 - Trưởng nhóm cộng đồng
  {
    name: 'Trưởng nhóm cộng đồng',
    link: ROUTES.COMMUNITY_LEADER,
    icon: <MdGroups />,
    requiredPermissions: ['PERM_COMMUNITY_LEADER_ACCESS'],
  },
  // R16 - Cộng tác viên AI
  {
    name: 'Cộng tác viên AI',
    link: ROUTES.AI_COLLABORATOR,
    icon: <MdSmartToy />,
    requiredPermissions: ['PERM_AI_COLLABORATOR_ACCESS'],
  },
]

export const baseRedirect: Record<string, string> = {
  // Layout Profile (Thông thường)
  R01: ROUTES.PROFILE, // Nông dân
  R02: ROUTES.PROFILE, // Người bán hàng
  R03: ROUTES.PROFILE, // Người tiêu dùng
  R04: ROUTES.PROFILE, // Chuyên gia
  R06: ROUTES.PROFILE, // Tài xế
  R07: ROUTES.PROFILE, // Nhà đầu tư
  R13: ROUTES.PROFILE, // Đại sứ TAP
  R15: ROUTES.PROFILE, // Trưởng nhóm cộng đồng
  R16: ROUTES.PROFILE, // Cộng tác viên AI

  // Layout Sidebar (Quản trị)
  R05: ROUTES.USER_MANAGEMENT, // Quản lý khu vực
  R08: ROUTES.EXECUTIVE_DASHBOARD, // Ban điều hành
  R09: ROUTES.SECTOR_MANAGER, // Người phụ trách ngành
  R10: ROUTES.AGRICULTURE_OFFICER, // Cán bộ nông nghiệp
  R11: ROUTES.TAX_FINANCE_OFFICER, // Cán bộ thuế & tài chính
  R12: ROUTES.INTERSECTORAL_OFFICER, // Cán bộ liên ngành
  R14: ROUTES.ASSESSMENT_SPECIALIST, // Chuyên viên thẩm định
}
